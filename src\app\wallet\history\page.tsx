"use client";

import { useSupabase } from "@/components/SessionProvider";
import { TransactionHistory } from "@/components/wallet/TransactionHistory";
import { WalletBalance } from "@/components/wallet/WalletBalance";
import { SolanaWalletBalance } from "@/components/wallet/SolanaWalletBalance";
import { StaticContentPage } from "@/components/StaticContentPage";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Suspense } from "react";

export default function TransactionHistoryPage() {
  const { session } = useSupabase();

  return (
    <StaticContentPage title="Transaction History">
      <div className="space-y-6">
        {!session ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Authentication Required</AlertTitle>
            <AlertDescription>
              Please log in to view your transaction history.
            </AlertDescription>
          </Alert>
        ) : (
          <Suspense fallback={<div>Loading...</div>}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <TransactionHistory />
              </div>
              <div className="space-y-4">
                <WalletBalance />
                <SolanaWalletBalance />
              </div>
            </div>
          </Suspense>
        )}
      </div>
    </StaticContentPage>
  );
}