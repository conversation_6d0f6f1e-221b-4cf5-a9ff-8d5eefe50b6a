"use client";

import { useState } from "react";
import { useEvmTransactions } from "@/hooks/useEvmTransactions";
import { useSolanaTransactions } from "@/hooks/useSolanaSafe";
import { useAccountSafe } from "@/hooks/useWagmiSafe";
import { useWallet } from "@solana/wallet-adapter-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { ArrowDownIcon, ArrowUpIcon, RefreshCw } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

export function TransactionHistory() {
  const [activeTab, setActiveTab] = useState<"evm" | "solana">("evm");
  
  // EVM wallet state
  const { address: evmAddress, isConnected: isEvmConnected } = useAccountSafe();
  const { 
    transactions: evmTransactions, 
    isLoading: isEvmLoading, 
    error: evmError,
    refetch: refetchEvmTransactions 
  } = useEvmTransactions();
  
  // Solana wallet state
  const { publicKey: solanaPublicKey, connected: isSolanaConnected } = useWallet();
  const { 
    transactions: solanaTransactions, 
    isLoading: isSolanaLoading, 
    error: solanaError,
    refetch: refetchSolanaTransactions 
  } = useSolanaTransactions(solanaPublicKey);

  // Handle refresh for the active tab
  const handleRefresh = () => {
    if (activeTab === "evm") {
      refetchEvmTransactions();
    } else {
      refetchSolanaTransactions();
    }
  };

  // Format transaction time
  const formatTransactionTime = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp * 1000), { addSuffix: true });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Transaction History</CardTitle>
            <CardDescription>View your recent transactions</CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleRefresh}
            disabled={(activeTab === "evm" && isEvmLoading) || (activeTab === "solana" && isSolanaLoading)}
          >
            <RefreshCw className={`h-4 w-4 ${(activeTab === "evm" && isEvmLoading) || (activeTab === "solana" && isSolanaLoading) ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="evm" onValueChange={(value) => setActiveTab(value as "evm" | "solana")}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="evm" disabled={!isEvmConnected}>EVM</TabsTrigger>
            <TabsTrigger value="solana" disabled={!isSolanaConnected}>Solana</TabsTrigger>
          </TabsList>
          
          <TabsContent value="evm">
            {!isEvmConnected ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">Connect your EVM wallet to view transactions</p>
              </div>
            ) : evmError ? (
              <div className="text-center py-6">
                <p className="text-red-500">Error loading transactions: {evmError}</p>
                <Button variant="outline" size="sm" className="mt-2" onClick={refetchEvmTransactions}>
                  Try Again
                </Button>
              </div>
            ) : isEvmLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </div>
            ) : evmTransactions.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">No transactions found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {evmTransactions.map((tx) => (
                  <div key={tx.hash} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div>
                      <div className="flex items-center gap-2">
                        {tx.from.toLowerCase() === evmAddress?.toLowerCase() ? (
                          <ArrowUpIcon className="h-4 w-4 text-red-500" />
                        ) : (
                          <ArrowDownIcon className="h-4 w-4 text-green-500" />
                        )}
                        <span className="font-medium">
                          {tx.from.toLowerCase() === evmAddress?.toLowerCase() ? 'Sent' : 'Received'}
                        </span>
                        <Badge variant={tx.status === 'success' ? 'outline' : tx.status === 'pending' ? 'secondary' : 'destructive'}>
                          {tx.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {formatTransactionTime(tx.timestamp)} • 
                        <a 
                          href={`https://etherscan.io/tx/${tx.hash}`} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:underline text-primary"
                        >
                          View
                        </a>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${tx.from.toLowerCase() === evmAddress?.toLowerCase() ? 'text-red-500' : 'text-green-500'}`}>
                        {tx.from.toLowerCase() === evmAddress?.toLowerCase() ? '-' : '+'}{tx.formattedValue}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="solana">
            {!isSolanaConnected ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">Connect your Solana wallet to view transactions</p>
              </div>
            ) : solanaError ? (
              <div className="text-center py-6">
                <p className="text-red-500">Error loading transactions: {solanaError}</p>
                <Button variant="outline" size="sm" className="mt-2" onClick={refetchSolanaTransactions}>
                  Try Again
                </Button>
              </div>
            ) : isSolanaLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </div>
            ) : solanaTransactions.length === 0 ? (
              <div className="text-center py-6">
                <p className="text-muted-foreground">No transactions found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {solanaTransactions.map((tx) => (
                  <div key={tx.signature} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Transaction</span>
                        <Badge variant={tx.err ? 'destructive' : 'outline'}>
                          {tx.err ? 'Failed' : 'Success'}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {formatTransactionTime(tx.blockTime)} • 
                        <a 
                          href={`https://explorer.solana.com/tx/${tx.signature}`} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:underline text-primary"
                        >
                          View
                        </a>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">
                        Slot: {tx.slot}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}