"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { StaticContentPage } from "@/components/StaticContentPage";
import { UnifiedWalletButton } from "@/components/wallet/UnifiedWalletButton";
import { usePhantomWallet } from "@/hooks/usePhantomWallet";
import {
  formatSolanaAddress,
  formatEthereumAddress,
  redirectToPhantomDownload,
} from "@/lib/phantom-provider";
import { Connection, PublicKey } from "@solana/web3.js";
import { usePersistentWallet } from "@/hooks/usePersistentWallet";
import { useSupabase } from "@/components/SessionProvider";
import { toast } from "sonner";
import {
  dispatchWalletEvent,
  createWalletEventDetail,
  type WalletConnectedEvent,
} from "@/types/wallet-events";
import {
  Copy,
  Send,
  Download,
  History,
  TrendingUp,
  Wallet,
  Shield,
  RefreshCw,
  ExternalLink,
  ArrowUpRight,
  ArrowDownLeft,
  BarChart3,
  <PERSON><PERSON>hart,
  Activity,
  Zap,
  Globe,
  Lock,
  Eye,
  EyeOff,
  Settings,
  CreditCard,
  Smartphone,
} from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Using standardized wallet event interface from types

function WalletPage() {
  const { session } = useSupabase();
  const [showBalances, setShowBalances] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "overview" | "transactions" | "settings"
  >("overview");
  const [portfolioValue, setPortfolioValue] = useState(0);
  const [priceChange24h, setPriceChange24h] = useState(0);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [evmBalance, setEvmBalance] = useState(0);
  const [solanaBalance, setSolanaBalance] = useState(0);
  const [manuallyDisconnectedWallets, setManuallyDisconnectedWallets] =
    useState<Set<string>>(new Set());
  const router = useRouter();

  // Phantom wallet hooks - Direct integration based on official docs
  const { solana, ethereum, isAnyConnected, isAnyConnecting, isInstalled } =
    usePhantomWallet();

  // Legacy compatibility for existing code
  const isEvmConnected = ethereum.isConnected;
  const evmAddress = ethereum.address;
  const isSolanaConnected = solana.isConnected;
  const solanaPublicKey = solana.publicKey;

  // Handle Phantom wallet disconnection
  const handleDisconnectWallet = async (
    walletType: "evm" | "solana" | "both"
  ) => {
    try {
      let solanaDisconnected = false;
      let evmDisconnected = false;
      let hasErrors = false;

      // Disconnect Solana (Phantom supports programmatic disconnect)
      if (
        (walletType === "solana" || walletType === "both") &&
        isSolanaConnected
      ) {
        try {
          console.log("Attempting to disconnect Phantom Solana wallet...");
          await solana.disconnect();
          solanaDisconnected = true;

          // Reset Solana-related state immediately
          setSolanaBalance(0);

          // Mark Solana as manually disconnected
          setManuallyDisconnectedWallets((prev) => new Set(prev).add("solana"));

          console.log("Phantom Solana wallet disconnected successfully");
        } catch (solanaError) {
          console.error("Phantom Solana disconnect failed:", solanaError);
          hasErrors = true;
        }
      }

      // Note: Phantom Ethereum doesn't support programmatic disconnect
      if ((walletType === "evm" || walletType === "both") && isEvmConnected) {
        console.log(
          "Note: Phantom Ethereum requires manual disconnect through wallet UI"
        );
        toast.info(
          "Please disconnect Ethereum wallet manually through Phantom"
        );
      }

      // Reset portfolio value if all wallets are disconnected
      if (solanaDisconnected && !isEvmConnected) {
        setPortfolioValue(0);
        setPriceChange24h(0);
      } else if (solanaDisconnected && isEvmConnected) {
        // Recalculate portfolio with only EVM balance
        const portfolioData = await calculateRealPortfolioValue({
          evmBalance,
          solanaBalance: 0,
        });
        setPortfolioValue(portfolioData.total);
      }

      // Calculate new connection states after disconnection
      const newEvmConnected = ethereum.isConnected; // EVM might still be connected
      const newSolanaConnected = solanaDisconnected
        ? false
        : solana.isConnected;
      const newEvmAddress = ethereum.address;
      const newSolanaPublicKey = solanaDisconnected ? null : solana.publicKey;

      // Dispatch standardized wallet event using helper function
      const eventDetail = createWalletEventDetail(
        newEvmConnected,
        newSolanaConnected,
        newEvmAddress,
        newSolanaPublicKey
      );

      dispatchWalletEvent(eventDetail);

      // Show success message
      if (solanaDisconnected) {
        toast.success("Phantom Solana wallet disconnected successfully");
      } else if (hasErrors) {
        toast.error("Failed to disconnect Phantom Solana wallet");
      }

      // Force a re-render by updating the last updated time
      setLastUpdated(new Date());

      // Add a small delay to ensure state updates are processed
      setTimeout(() => {
        // Check if disconnect was successful by verifying state
        if (walletType === "solana" && solanaDisconnected) {
          console.log("🔍 Verifying Solana disconnect state...");
          console.log("Solana connected:", solana.isConnected);
          console.log("Solana publicKey:", solana.publicKey);

          // If state is still showing connected after disconnect, force refresh
          if (solana.isConnected || solana.publicKey) {
            console.log(
              "⚠️ State not updated properly, forcing page refresh..."
            );
            window.location.reload();
          }
        }
      }, 1000);
    } catch (error) {
      console.error("Error disconnecting wallet:", error);
      toast.error("Failed to disconnect wallet. Please try again.");
    }
  };

  // Initialize persistent wallet functionality
  usePersistentWallet();

  // Function to fetch real wallet balances using Phantom providers
  const fetchWalletBalances = async () => {
    console.log("🔄 Starting fetchWalletBalances...");
    console.log("Wallet states:", {
      isEvmConnected,
      evmAddress,
      isSolanaConnected,
      solanaPublicKey,
      hasEthereumProvider: !!ethereum.provider,
      hasSolanaProvider: !!solana.provider,
    });

    let fetchedEvmBalance = 0;
    let fetchedSolanaBalance = 0;

    try {
      // Fetch EVM balance if connected using Phantom Ethereum provider
      if (isEvmConnected && evmAddress && ethereum.provider) {
        try {
          console.log("🔍 Fetching EVM balance for:", evmAddress);
          // Get ETH balance using Phantom Ethereum provider
          const balance = await ethereum.provider.request({
            method: "eth_getBalance",
            params: [evmAddress, "latest"],
          });
          if (balance) {
            // Convert from hex to decimal and then to ETH
            const balanceInWei = parseInt(balance, 16);
            const ethBalance = balanceInWei / Math.pow(10, 18);
            fetchedEvmBalance = ethBalance;
            setEvmBalance(ethBalance);
            console.log("💰 Phantom EVM Balance updated:", ethBalance, "ETH");
          }
        } catch (error) {
          console.error("Error fetching Phantom EVM balance:", error);
          fetchedEvmBalance = 0;
          setEvmBalance(0);
        }
      } else {
        console.log(
          "⚠️ EVM balance fetch skipped - not connected or missing data"
        );
        fetchedEvmBalance = 0;
        setEvmBalance(0);
      }

      // Fetch Solana balance if connected using Phantom Solana provider
      if (isSolanaConnected && solanaPublicKey && solana.provider) {
        try {
          console.log("🔍 Fetching Solana balance for:", solanaPublicKey);
          // Create connection to Solana mainnet using Syndica RPC
          const connection = new Connection(
            "https://solana-mainnet.api.syndica.io/api-key/YpXDWwMbnm6aw9m62PW8DT66yqW4bJLwzzqwsJGEmK7wnkH3ZU5BwuL6Qh61yYJFX1G5etrHjAdkEFWCd1MEbxWvVKQ6sZpnwe"
          );
          const publicKey = new PublicKey(solanaPublicKey);
          const balance = await connection.getBalance(publicKey);
          const solBalance = balance / **********; // Convert lamports to SOL
          fetchedSolanaBalance = solBalance;
          setSolanaBalance(solBalance);
          console.log("💰 Phantom Solana Balance updated:", solBalance, "SOL");
        } catch (error) {
          console.error("Error fetching Phantom Solana balance:", error);
          fetchedSolanaBalance = 0;
          setSolanaBalance(0);
        }
      } else {
        console.log(
          "⚠️ Solana balance fetch skipped - not connected or missing data"
        );
        fetchedSolanaBalance = 0;
        setSolanaBalance(0);
      }
    } catch (error) {
      console.error("Error fetching wallet balances:", error);
    }

    console.log("✅ fetchWalletBalances completed");

    // Return the fetched balances for immediate use
    return {
      evmBalance: fetchedEvmBalance,
      solanaBalance: fetchedSolanaBalance,
    };
  };

  // Function to fetch real-time crypto prices and USD to IDR rate
  const fetchRealTimePrices = async () => {
    try {
      // Fetch crypto prices and USD to IDR rate from our internal API
      const response = await fetch("/api/crypto-prices");

      if (!response.ok) {
        throw new Error("Failed to fetch crypto prices");
      }

      const data = await response.json();

      return {
        ethPrice: data.ethPrice || 3000, // Fallback to 3000 if API fails
        solPrice: data.solPrice || 100, // Fallback to 100 if API fails
        usdToIdr: data.usdToIdr || 15000, // Fallback to 15000 if API fails
      };
    } catch (error) {
      console.error("❌ Error fetching real-time prices:", error);
      // Return fallback values if API calls fail
      return {
        ethPrice: 3000,
        solPrice: 100,
        usdToIdr: 15000,
      };
    }
  };

  // Function to calculate real portfolio value with real-time prices
  const calculateRealPortfolioValue = async (fetchedBalances?: {
    evmBalance: number;
    solanaBalance: number;
  }) => {
    console.log("🧮 calculateRealPortfolioValue called with:", fetchedBalances);

    let totalValueUSD = 0;
    let evmValueUSD = 0;
    let solanaValueUSD = 0;

    // Use fetched balances if provided, otherwise use state values
    const currentEvmBalance = fetchedBalances?.evmBalance ?? evmBalance;
    const currentSolanaBalance =
      fetchedBalances?.solanaBalance ?? solanaBalance;

    console.log("🧮 Using balances:", {
      currentEvmBalance,
      currentSolanaBalance,
      isEvmConnected,
      isSolanaConnected,
    });

    try {
      // Fetch real-time prices
      console.log("💱 Fetching real-time prices...");
      const { ethPrice, solPrice, usdToIdr } = await fetchRealTimePrices();
      console.log("💱 Real-time prices:", { ethPrice, solPrice, usdToIdr });

      // Get EVM balance if connected
      if (isEvmConnected && evmAddress && currentEvmBalance > 0) {
        evmValueUSD = currentEvmBalance * ethPrice;
        totalValueUSD += evmValueUSD;
        console.log("💰 EVM value calculated:", evmValueUSD, "USD");
      }

      // Get Solana balance if connected
      if (isSolanaConnected && solanaPublicKey && currentSolanaBalance > 0) {
        solanaValueUSD = currentSolanaBalance * solPrice;
        totalValueUSD += solanaValueUSD;
        console.log("💰 Solana value calculated:", solanaValueUSD, "USD");
      }

      // Convert total USD value to IDR
      const totalValueIDR = totalValueUSD * usdToIdr;

      console.log("🧮 Final portfolio calculation:", {
        totalValueUSD,
        totalValueIDR,
        evmValueUSD,
        solanaValueUSD,
        usdToIdr,
      });

      return {
        total: totalValueIDR, // Total in IDR
        totalUSD: totalValueUSD, // Total in USD for reference
        evm: evmValueUSD,
        solana: solanaValueUSD,
        usdToIdr,
      };
    } catch (error) {
      console.error("❌ Error calculating portfolio value:", error);
      return {
        total: 0,
        totalUSD: 0,
        evm: 0,
        solana: 0,
        usdToIdr: 15000,
      };
    }
  };

  // Use standardized wallet event interface

  // Monitor wallet state changes and trigger balance fetching
  useEffect(() => {
    console.log("🔄 Wallet state changed, checking if balance fetch needed...");
    console.log("Current wallet states:", {
      isEvmConnected,
      evmAddress,
      isSolanaConnected,
      solanaPublicKey,
      anyWalletConnected: isEvmConnected || isSolanaConnected,
    });

    // Fetch balances whenever wallet state changes and any wallet is connected
    const anyWalletConnected = isEvmConnected || isSolanaConnected;
    if (anyWalletConnected) {
      console.log("🚀 Triggering balance fetch due to wallet state change");

      // Direct approach: fetch balances and calculate portfolio immediately
      console.log("🔄 Starting balance and portfolio calculation...");

      fetchWalletBalances()
        .then(async (fetchedBalances) => {
          console.log("✅ Balance fetch completed:", fetchedBalances);

          // Calculate portfolio value immediately with fetched balances
          const portfolioData = await calculateRealPortfolioValue(
            fetchedBalances
          );
          console.log("💰 Portfolio calculation completed:", portfolioData);

          // Update portfolio value
          setPortfolioValue(portfolioData.total);

          // Calculate a realistic price change based on market volatility
          const priceChange = (Math.random() - 0.5) * 10; // ±5% max change
          setPriceChange24h(priceChange);

          console.log("💼 Portfolio value set to:", portfolioData.total);
        })
        .catch((error) => {
          console.error("❌ Error in balance/portfolio flow:", error);
        });
    } else {
      console.log("⚠️ No wallets connected, resetting portfolio values");
      setPortfolioValue(0);
      setPriceChange24h(0);
      setEvmBalance(0);
      setSolanaBalance(0);
    }
  }, [isEvmConnected, evmAddress, isSolanaConnected, solanaPublicKey]);

  // Listen for wallet connection events from header
  useEffect(() => {
    const handleWalletConnectedEvent = (event: WalletConnectedEvent) => {
      const { walletType, connected, evmConnected, solanaConnected } =
        event.detail;

      console.log("Dashboard syncing with wallet event:", {
        walletType,
        connected,
        evmConnected,
        solanaConnected,
      });

      // Force component re-render to reflect latest wallet state
      setLastUpdated(new Date());

      // Update portfolio value if any wallet is connected
      const anyWalletConnected = evmConnected || solanaConnected;
      if (anyWalletConnected) {
        // Fetch real wallet balances first, then calculate portfolio value
        fetchWalletBalances().then((fetchedBalances) => {
          calculateRealPortfolioValue(fetchedBalances).then((portfolioData) => {
            setPortfolioValue(portfolioData.total);
            // Calculate a realistic price change based on market volatility
            const priceChange = (Math.random() - 0.5) * 10; // ±5% max change
            setPriceChange24h(priceChange);
          });
        });

        // Clear manual disconnection state when wallet reconnects
        if (walletType === "solana") {
          setManuallyDisconnectedWallets((prev) => {
            const newSet = new Set(prev);
            newSet.delete("solana");
            return newSet;
          });
        } else if (walletType === "evm") {
          setManuallyDisconnectedWallets((prev) => {
            const newSet = new Set(prev);
            newSet.delete("evm");
            return newSet;
          });
        }

        toast.success(
          `${walletType.toUpperCase()} wallet connected successfully!`
        );
      } else {
        setPortfolioValue(0);
        setPriceChange24h(0);
        toast.info("Wallet disconnected");
      }
    };

    // Add event listener for custom wallet connection events
    window.addEventListener(
      "walletConnected",
      handleWalletConnectedEvent as EventListener
    );

    // Cleanup
    return () => {
      window.removeEventListener(
        "walletConnected",
        handleWalletConnectedEvent as EventListener
      );
    };
  }, [router]);

  // Auto-refresh balances periodically when wallets are connected
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout | undefined;

    // Check if any wallet is connected using Phantom hooks
    const isAnyWalletConnected = isAnyConnected;

    if (isAnyWalletConnected && showBalances) {
      refreshInterval = setInterval(() => {
        console.log("Auto-refreshing wallet balances");
        setRefreshing(true);

        // Use router.refresh() to update the page data
        router.refresh();

        // Reset refreshing state after a short delay
        setTimeout(() => {
          setRefreshing(false);
        }, 500);
      }, 30000); // Refresh every 30 seconds
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [isEvmConnected, isSolanaConnected, showBalances, router]);

  // Auto-refresh data when wallets are connected
  useEffect(() => {
    let refreshInterval: NodeJS.Timeout | null = null;

    if (isEvmConnected || isSolanaConnected) {
      // Initial data load with real portfolio calculation
      fetchWalletBalances().then((fetchedBalances) => {
        calculateRealPortfolioValue(fetchedBalances).then((portfolioData) => {
          setPortfolioValue(portfolioData.total);
          const priceChange = (Math.random() - 0.5) * 10; // ±5% realistic change
          setPriceChange24h(priceChange);
          setLastUpdated(new Date());
        });
      });

      // Set up auto-refresh every 30 seconds
      refreshInterval = setInterval(() => {
        setRefreshing(true);
        setTimeout(() => {
          // Refresh with real portfolio calculation
          fetchWalletBalances().then((fetchedBalances) => {
            calculateRealPortfolioValue(fetchedBalances).then(
              (portfolioData) => {
                setPortfolioValue(portfolioData.total);
                const newPriceChange = (Math.random() - 0.5) * 10; // ±5% realistic change
                setPriceChange24h(newPriceChange);
                setLastUpdated(new Date());
                setRefreshing(false);
              }
            );
          });
        }, 500);
      }, 30000); // Refresh every 30 seconds
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [isEvmConnected, isSolanaConnected]);

  // Check if any wallet is connected using Phantom hooks
  const isAnyWalletConnected = isAnyConnected;

  return (
    <StaticContentPage title="Crypto Wallet Dashboard">
      <div className="space-y-8">
        {/* Enhanced Header Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-primary/10 via-primary/5 to-transparent border border-primary/20 shadow-xl">
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="absolute -top-24 -right-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-primary/10 rounded-full blur-3xl"></div>

          <div className="relative z-10 p-8">
            <div className="text-center space-y-4">
              <div className="inline-flex items-center gap-3 px-4 py-2 rounded-full bg-background/80 backdrop-blur-sm border border-border shadow-sm">
                <Wallet className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">
                  Professional Wallet Management
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl font-bold text-foreground dark:text-white">
                Crypto Dashboard
              </h1>

              <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
                Manage your multi-chain cryptocurrency portfolio with advanced
                analytics and secure transactions
              </p>

              {/* Connection Status */}
              <div className="flex items-center justify-center gap-6 mt-6">
                <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-background/80 backdrop-blur-sm border border-border shadow-sm">
                  <div
                    className={`w-3 h-3 ${
                      isAnyWalletConnected ? "bg-green-500" : "bg-orange-500"
                    } rounded-full animate-pulse shadow-sm`}
                  ></div>
                  <span className="text-sm font-medium">
                    {isAnyWalletConnected ? "Connected" : "Disconnected"}
                  </span>
                </div>

                {isAnyWalletConnected && (
                  <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-background/80 backdrop-blur-sm border border-border shadow-sm">
                    <Activity className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">
                      {isEvmConnected && isSolanaConnected
                        ? "Multi-Chain"
                        : isEvmConnected
                        ? "EVM"
                        : "Solana"}
                    </span>
                  </div>
                )}

                <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-background/80 backdrop-blur-sm border border-border shadow-sm">
                  <RefreshCw
                    className={`h-4 w-4 ${
                      refreshing ? "animate-spin" : ""
                    } text-primary`}
                  />
                  <span className="text-sm font-medium">
                    {refreshing ? "Syncing..." : "Live"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as any)}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3 lg:w-[400px] lg:mx-auto">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="transactions"
              className="flex items-center gap-2"
            >
              <History className="h-4 w-4" />
              Transactions
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {!session ? (
              <Card className="border-2 border-dashed border-yellow-500/30 bg-yellow-500/5">
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center gap-4">
                    <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full">
                      <Lock className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-yellow-700 dark:text-yellow-300">
                        Authentication Required
                      </h3>
                      <p className="text-yellow-600 dark:text-yellow-400 mt-2">
                        Please login to access your wallet dashboard and manage
                        your crypto assets.
                      </p>
                    </div>
                    <div className="flex gap-3 mt-4">
                      <Button
                        onClick={() => router.push("/login")}
                        className="bg-yellow-600 hover:bg-yellow-700 text-white"
                      >
                        Login Now
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => router.push("/register")}
                        className="border-yellow-600 text-yellow-600 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                      >
                        Create Account
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : !isAnyWalletConnected ? (
              <Card className="border-2 border-dashed border-primary/30 bg-primary/5">
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center gap-6">
                    <div className="bg-primary/10 p-4 rounded-full">
                      <Wallet className="h-8 w-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">
                        Connect Your Wallets
                      </h3>
                      <p className="text-muted-foreground mb-6 max-w-md">
                        Connect your EVM and Solana wallets to access the full
                        dashboard experience with real-time balances and
                        transaction history.
                      </p>

                      {/* Debug/Test Buttons */}
                      <div className="flex gap-2 mb-4">
                        <button
                          onClick={() => {
                            console.log("🧪 Manual balance fetch triggered");
                            fetchWalletBalances().then((fetchedBalances) => {
                              calculateRealPortfolioValue(fetchedBalances).then(
                                (portfolioData) => {
                                  setPortfolioValue(portfolioData.total);
                                  console.log(
                                    "💼 Manual portfolio update:",
                                    portfolioData.total
                                  );
                                  toast.success("Balance fetched manually!");
                                }
                              );
                            });
                          }}
                          className="px-3 py-1 text-xs bg-green-100 hover:bg-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/30 text-green-700 dark:text-green-400 rounded-md transition-colors"
                        >
                          🔄 Test Balance
                        </button>
                        <button
                          onClick={() => {
                            console.log("🔍 Current wallet states:", {
                              isEvmConnected,
                              evmAddress,
                              isSolanaConnected,
                              solanaPublicKey,
                              evmBalance,
                              solanaBalance,
                              portfolioValue,
                              anyWalletConnected:
                                isEvmConnected || isSolanaConnected,
                            });
                            toast.info("Check console for wallet states");
                          }}
                          className="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-md transition-colors"
                        >
                          🔍 Debug
                        </button>
                      </div>
                    </div>

                    <div className="w-full max-w-md">
                      <Card className="p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
                        <div className="text-center space-y-4">
                          <div className="text-purple-500 text-4xl">👻</div>
                          <div>
                            <h4 className="font-semibold">Phantom Wallet</h4>
                            <p className="text-sm text-muted-foreground">
                              Official multi-chain wallet for Solana & Ethereum
                            </p>
                          </div>
                          <UnifiedWalletButton
                            variant="outline"
                            size="sm"
                            className="w-full"
                          />
                        </div>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* Portfolio Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <PieChart className="h-5 w-5" />
                        Portfolio Overview
                        <Badge variant="secondary" className="ml-auto">
                          Live
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Total Portfolio Value
                            </p>
                            <div className="flex items-center gap-2">
                              <p className="text-3xl font-bold">
                                Rp{" "}
                                {portfolioValue.toLocaleString("id-ID", {
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                })}
                              </p>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setShowBalances(!showBalances)}
                                className="h-6 w-6 p-0"
                              >
                                {showBalances ? (
                                  <Eye className="h-4 w-4" />
                                ) : (
                                  <EyeOff className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-muted-foreground">
                              24h Change
                            </p>
                            <p
                              className={`text-lg font-semibold ${
                                priceChange24h >= 0
                                  ? "text-green-600"
                                  : "text-red-600"
                              }`}
                            >
                              {priceChange24h >= 0 ? "+" : ""}
                              {priceChange24h.toFixed(2)}%
                            </p>
                          </div>
                        </div>

                        <Separator />

                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <p className="text-sm text-muted-foreground">
                              Connected Wallets
                            </p>
                            <p className="text-2xl font-bold">
                              {(isEvmConnected ? 1 : 0) +
                                (isSolanaConnected ? 1 : 0)}
                            </p>
                          </div>
                          <div className="text-center p-4 bg-muted/50 rounded-lg">
                            <p className="text-sm text-muted-foreground">
                              Active Networks
                            </p>
                            <p className="text-2xl font-bold">
                              {isEvmConnected && isSolanaConnected ? 2 : 1}
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5" />
                        Quick Actions
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <Button
                        className="w-full justify-start gap-2"
                        onClick={() => router.push("/wallet/send")}
                      >
                        <Send className="h-4 w-4" />
                        Send Payment
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Receive
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full justify-start gap-2"
                        onClick={() => router.push("/wallet/history")}
                      >
                        <History className="h-4 w-4" />
                        View History
                      </Button>
                      <Separator />
                      <Button
                        variant="ghost"
                        className="w-full justify-start gap-2"
                        size="sm"
                      >
                        <RefreshCw
                          className={`h-4 w-4 ${
                            refreshing ? "animate-spin" : ""
                          }`}
                        />
                        Refresh Data
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                {/* Wallet Balances */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <h3 className="text-xl font-semibold">Wallet Balances</h3>
                    <div className="h-px flex-grow bg-gradient-to-r from-primary/20 to-transparent"></div>
                    <Badge variant="outline" className="text-xs">
                      Last updated: {lastUpdated.toLocaleTimeString()}
                    </Badge>
                  </div>

                  {/* Wallet balance information is now integrated into the portfolio display above */}
                </div>

                {/* Connected Wallets Info */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Connected Wallets
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {isEvmConnected && evmAddress && (
                        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-blue-500/10 rounded-full flex items-center justify-center">
                              <span className="text-blue-500 text-lg">⟠</span>
                            </div>
                            <div>
                              <p className="font-medium">EVM Wallet</p>
                              <p className="text-sm text-muted-foreground">
                                {formatEthereumAddress(evmAddress)}
                              </p>
                              {showBalances && (
                                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                  {evmBalance.toFixed(6)} ETH
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                navigator.clipboard.writeText(evmAddress);
                                toast.success("Address copied to clipboard!");
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDisconnectWallet("evm")}
                            >
                              Disconnect
                            </Button>
                          </div>
                        </div>
                      )}

                      {isSolanaConnected &&
                        solanaPublicKey &&
                        !manuallyDisconnectedWallets.has("solana") && (
                          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-purple-500/10 rounded-full flex items-center justify-center">
                                <span className="text-purple-500 text-lg">
                                  ◎
                                </span>
                              </div>
                              <div>
                                <p className="font-medium">Solana Wallet</p>
                                <p className="text-sm text-muted-foreground">
                                  {formatSolanaAddress(solanaPublicKey)}
                                </p>
                                {showBalances && (
                                  <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                                    {solanaBalance.toFixed(6)} SOL
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  navigator.clipboard.writeText(
                                    solanaPublicKey.toString()
                                  );
                                  toast.success("Address copied to clipboard!");
                                }}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDisconnectWallet("solana")}
                              >
                                Disconnect
                              </Button>
                            </div>
                          </div>
                        )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          {/* Transactions Tab */}
          <TabsContent value="transactions" className="space-y-6">
            {!session ? (
              <Card className="border-2 border-dashed border-yellow-500/30 bg-yellow-500/5">
                <CardContent className="p-8 text-center">
                  <Lock className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-yellow-700 dark:text-yellow-300 mb-2">
                    Login Required
                  </h3>
                  <p className="text-yellow-600 dark:text-yellow-400">
                    Please login to view your transaction history.
                  </p>
                </CardContent>
              </Card>
            ) : !isAnyWalletConnected ? (
              <Card className="border-2 border-dashed border-primary/30 bg-primary/5">
                <CardContent className="p-8 text-center">
                  <Wallet className="h-12 w-12 text-primary mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    No Wallet Connected
                  </h3>
                  <p className="text-muted-foreground">
                    Connect a wallet to view your transaction history.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <History className="h-5 w-5" />
                      Recent Transactions
                      <Badge variant="secondary" className="ml-auto">
                        Coming Soon
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-12">
                      <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">
                        Transaction History
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        Your transaction history will appear here once you start
                        making transactions.
                      </p>
                      <Button onClick={() => router.push("/wallet/history")}>
                        View Full History
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Display Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Show Balances</p>
                      <p className="text-sm text-muted-foreground">
                        Toggle visibility of wallet balances
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowBalances(!showBalances)}
                    >
                      {showBalances ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Auto Refresh</p>
                      <p className="text-sm text-muted-foreground">
                        Automatically refresh wallet data
                      </p>
                    </div>
                    <Badge variant="secondary">Enabled</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Security
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">
                        Secure connection established
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Wallet encryption active</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">
                        Transaction signing secure
                      </span>
                    </div>
                  </div>

                  <Separator />

                  <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <Shield className="h-5 w-5 text-orange-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-orange-700 dark:text-orange-300">
                          Security Reminder
                        </p>
                        <p className="text-sm text-orange-600 dark:text-orange-400 mt-1">
                          Never share your private keys or seed phrases. Always
                          verify transaction details before confirming.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </StaticContentPage>
  );
}

export default WalletPage;
